package util

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/Knetic/govaluate"
)

var expFuncMap = map[string]govaluate.ExpressionFunction{
	"expN": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Pow(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
	"log10": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log10(arguments[0].(float64))
		return rst, nil
	},
	"log2": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log2(arguments[0].(float64))
		return rst, nil
	},
	"log": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Log(arguments[0].(float64))
		return rst, nil
	},
	"max": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Max(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
	"min": func(arguments ...interface{}) (interface{}, error) {
		rst := math.Min(arguments[0].(float64), arguments[1].(float64))
		return rst, nil
	},
}

// BuildExpression expFuncMap 全局变量，无需指定
func BuildExpression(ctx context.Context, expString string) *govaluate.EvaluableExpression {
	var expression *govaluate.EvaluableExpression
	var err error
	expression, err = govaluate.NewEvaluableExpressionWithFunctions(expString, expFuncMap)
	if err != nil {
		logkit.FromContext(ctx).Error("get expression failed", logkit.String("expString", expString), logkit.Any("expFunc", expFuncMap))
		return nil
	}
	return expression
}

func BuildExpParameters(expParameters string) map[string]interface{} {
	parameters := make(map[string]interface{})
	if len(expParameters) == 0 {
		return parameters
	}
	for _, pair := range strings.Split(expParameters, ",") {
		keyValue := strings.Split(pair, "=")
		if len(keyValue) < 2 {
			continue
		}
		key := keyValue[0]
		value, err := strconv.ParseFloat(keyValue[1], 64)
		if err != nil {
			continue
		}
		parameters[key] = value
	}
	return parameters
}

// EvaluateScore 优化版本：减少日志开销，提高性能
func EvaluateScore(ctx context.Context, expression *govaluate.EvaluableExpression, parameters map[string]interface{}) (float64, error) {
	if expression == nil {
		return 0.0, errors.New("expression is nil")
	}

	val, err := expression.Evaluate(parameters)
	if err != nil {
		// 只在出错时记录日志，减少正常流程的日志开销
		logkit.FromContext(ctx).WithError(err).Error("EvaluateScore failed to evaluate expression",
			logkit.String("expression", expression.String()),
			logkit.Int("param_count", len(parameters)))
		return 0.0, err
	}

	if val == nil {
		return 0.0, errors.New("evaluation result is nil")
	}

	// 优化类型断言，先检查类型
	score, ok := val.(float64)
	if !ok {
		logkit.FromContext(ctx).Error("EvaluateScore result type assertion failed",
			logkit.String("expression", expression.String()),
			logkit.String("result_type", fmt.Sprintf("%T", val)))
		return 0.0, errors.New("result is not float64")
	}

	if math.IsNaN(score) {
		logkit.FromContext(ctx).Error("EvaluateScore result is NaN",
			logkit.String("expression", expression.String()))
		return 0.0, errors.New("score is NaN")
	}

	return score, nil
}

// EvaluateScoreFast 高性能版本：用于高频调用场景，最小化错误检查和日志
// 调用方需要确保 expression 不为 nil
func EvaluateScoreFast(expression *govaluate.EvaluableExpression, parameters map[string]interface{}) (float64, error) {
	val, err := expression.Evaluate(parameters)
	if err != nil {
		return 0.0, err
	}

	if val == nil {
		return 0.0, errors.New("evaluation result is nil")
	}

	score, ok := val.(float64)
	if !ok {
		return 0.0, errors.New("result is not float64")
	}

	if math.IsNaN(score) {
		return 0.0, errors.New("score is NaN")
	}

	return score, nil
}
