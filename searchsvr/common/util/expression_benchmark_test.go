package util

import (
	"context"
	"testing"

	"github.com/Knetic/govaluate"
)

// 基准测试：对比原版本和优化版本的性能
func BenchmarkEvaluateScore(b *testing.B) {
	// 准备测试数据
	exp := "expN(listwise_ctr, w1)*expN(listwise_cvr, w2)*expN(pue, w3)*expN(relevance, w4)"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"listwise_ctr": 0.025889545679092407,
		"listwise_cvr": 0.016622215509414673,
		"pue":          0.694241,
		"relevance":    -0.0062328028,
		"w1":           1.0,
		"w2":           1.2,
		"w3":           0.1,
		"w4":           0.01,
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScore(ctx, expression, parameters)
	}
}

func BenchmarkEvaluateScoreFast(b *testing.B) {
	// 准备测试数据
	exp := "expN(listwise_ctr, w1)*expN(listwise_cvr, w2)*expN(pue, w3)*expN(relevance, w4)"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"listwise_ctr": 0.025889545679092407,
		"listwise_cvr": 0.016622215509414673,
		"pue":          0.694241,
		"relevance":    -0.0062328028,
		"w1":           1.0,
		"w2":           1.2,
		"w3":           0.1,
		"w4":           0.01,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScoreFast(expression, parameters)
	}
}

// 测试简单表达式的性能
func BenchmarkEvaluateScoreSimple(b *testing.B) {
	exp := "a * b + c"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"a": 1.5,
		"b": 2.0,
		"c": 0.5,
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScore(ctx, expression, parameters)
	}
}

func BenchmarkEvaluateScoreFastSimple(b *testing.B) {
	exp := "a * b + c"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"a": 1.5,
		"b": 2.0,
		"c": 0.5,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScoreFast(expression, parameters)
	}
}

// 测试复杂表达式的性能
func BenchmarkEvaluateScoreComplex(b *testing.B) {
	exp := "log10(max(a, 0.001)) * expN(b, 2) + min(c, 1.0) * log(d + 1)"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"a": 0.025,
		"b": 0.016,
		"c": 0.694,
		"d": 0.006,
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScore(ctx, expression, parameters)
	}
}

func BenchmarkEvaluateScoreFastComplex(b *testing.B) {
	exp := "log10(max(a, 0.001)) * expN(b, 2) + min(c, 1.0) * log(d + 1)"
	expression := BuildExpression(context.Background(), exp)
	
	parameters := map[string]interface{}{
		"a": 0.025,
		"b": 0.016,
		"c": 0.694,
		"d": 0.006,
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = EvaluateScoreFast(expression, parameters)
	}
}

// 测试批量调用场景（模拟实际使用场景）
func BenchmarkEvaluateScoreBatch(b *testing.B) {
	exp := "expN(ctr, w1) * expN(cvr, w2) * expN(relevance, w3)"
	expression := BuildExpression(context.Background(), exp)
	
	// 模拟100个店铺的参数
	batchParams := make([]map[string]interface{}, 100)
	for i := 0; i < 100; i++ {
		batchParams[i] = map[string]interface{}{
			"ctr":       0.01 + float64(i)*0.001,
			"cvr":       0.005 + float64(i)*0.0005,
			"relevance": 0.1 + float64(i)*0.01,
			"w1":        1.0,
			"w2":        1.2,
			"w3":        0.8,
		}
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, params := range batchParams {
			_, _ = EvaluateScore(ctx, expression, params)
		}
	}
}

func BenchmarkEvaluateScoreFastBatch(b *testing.B) {
	exp := "expN(ctr, w1) * expN(cvr, w2) * expN(relevance, w3)"
	expression := BuildExpression(context.Background(), exp)
	
	// 模拟100个店铺的参数
	batchParams := make([]map[string]interface{}, 100)
	for i := 0; i < 100; i++ {
		batchParams[i] = map[string]interface{}{
			"ctr":       0.01 + float64(i)*0.001,
			"cvr":       0.005 + float64(i)*0.0005,
			"relevance": 0.1 + float64(i)*0.01,
			"w1":        1.0,
			"w2":        1.2,
			"w3":        0.8,
		}
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		for _, params := range batchParams {
			_, _ = EvaluateScoreFast(expression, params)
		}
	}
}
