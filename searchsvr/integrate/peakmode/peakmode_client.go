package peakmode

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_peakmode"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"go.uber.org/zap"
)

var (
	strScene              = "DeliveryDistance"
	strSrc                = "search_old"
	uServiceType    int64 = 0
	defaultDistance       = 15000
)

func GetDeliveryDistance(ctx context.Context, traceInfo *traceinfo.TraceInfo, lng, lat float64, cityId int64) uint32 {
	if cityId == 0 {
		return 0
	}
	req := &foodalgo_peakmode.GetSceneConfigRequest{
		Scene:       &strScene,
		Src:         &strSrc,
		ServiceType: &uServiceType,
		Location: &foodalgo_peakmode.Location{
			Longitude: &lng,
			Latitude:  &lat,
			CityId:    &cityId,
		},
	}
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.PeakModeDistanceTimeOut
	if timeout <= 0 {
		timeout = 100
	}
	// 请求peak mode client召回
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	st := time.Now()
	rsp, err := integrate.PeakModeClient.GetSceneConfig(ctx, req)
	logger.MyDebug(ctx, traceInfo.IsDebug, "peakmode distance: Call PeakModeClient.GetSceneConfig", zap.Any("req", req), zap.Any("rsp", rsp))
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "PeakMode.distance"), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "PeakMode.distance")
	if err != nil {
		logkit.FromContext(ctx).Error("peakmode distance: get delivery distance from peak mode failed, default with 15km", logkit.Err(err), logkit.Any("req", req), logkit.String("cost", time.Since(st).String()))
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		return uint32(defaultDistance)
	}
	deliveryDistance := util.StringToUint64(rsp.GetValue())
	if deliveryDistance > 0 {
		return uint32(deliveryDistance)
	}
	return 0
}
