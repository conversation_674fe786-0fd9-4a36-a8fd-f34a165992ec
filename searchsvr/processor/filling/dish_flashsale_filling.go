package filling

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func DishFlashSaleFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if len(dishes) == 0 {
		return
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
		return
	}

	// 1. 先把带FlashSale的菜品挑出来
	containsFlashSaleDishes := make(model.DishInfos, 0)
	for _, dish := range dishes {
		if dish.IsFlashSale {
			containsFlashSaleDishes = append(containsFlashSaleDishes, dish)
		}
	}
	if len(containsFlashSaleDishes) == 0 {
		return
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingDishFlashSale, time.Since(pt))
	}()

	// 2. 每个菜品对应多个flashSaleIds，二维展开
	totalFlashSaleIds := make([]uint64, 0)
	for _, dish := range containsFlashSaleDishes {
		totalFlashSaleIds = append(totalFlashSaleIds, dish.FlashSaleIds...)
	}

	// 3.totalFlashSaleIds 缓存过滤
	pt1 := time.Now()
	flashSaleMap := make(map[uint64]*o2oalgo.FlashSaleDishDiscount, len(totalFlashSaleIds))
	noCacheIds := localcache.StoreCacheSysInstance.MetGetDishFlashSaleCacheBatch(ctx, totalFlashSaleIds, flashSaleMap)
	traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingDishFlashSaleGetCache, time.Since(pt1))

	// 4.上报监控
	totalLen := len(totalFlashSaleIds)
	hitLen := totalLen
	defer func() {
		metric_reporter2.ReportHitRateRequest(totalLen, hitLen, "DishFlashSaleFreeCache")
	}()

	// 5.update cache
	if len(noCacheIds) > 0 {
		hitLen = totalLen - len(noCacheIds)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish flash sale filling with rpc", logkit.Any("flashSaleIds", noCacheIds))
		pt2 := time.Now()
		rpcFlashSaleInfos, err := GetFlashSaleInfos(ctx, traceInfo, noCacheIds)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish flash sale filling with rpc", logkit.Any("rpcFlashSaleInfos", rpcFlashSaleInfos))
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingDishFlashSaleRPC, time.Since(pt2))
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("GetFlashSaleInfos failed")
		}
		for flashSaleId, flashSale := range rpcFlashSaleInfos {
			flashSaleMap[flashSaleId] = flashSale
		}

		// 批量写入缓存
		goroutine.WithGo(ctx, "MSetDishFlashSaleCache", func(params ...interface{}) {
			pt3 := time.Now()
			localcache.StoreCacheSysInstance.MSetDishFlashSaleCache(ctx, rpcFlashSaleInfos, 1800)
			traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingDishFlashSaleSetCache, time.Since(pt3))
		})
	}

	// 6.只对当且仅当flashSale的菜品更新，打标
	currentTimestamp := uint64(time.Now().Unix()) * 1000 // 毫秒
	for _, dishInfo := range containsFlashSaleDishes {
		for _, flashSaleId := range dishInfo.FlashSaleIds {
			if flashSaleDiscount, ok := flashSaleMap[flashSaleId]; ok && flashSaleDiscount != nil {
				if isFlashSaleValid(traceInfo, flashSaleDiscount, currentTimestamp) {
					// 如果这个菜品属于多个flashSale，则折扣按照最高计算
					dishInfo.FlashSaleDiscountPercentage = util.MaxInt64(dishInfo.FlashSaleDiscountPercentage, flashSaleDiscount.GetPercentage())
					dishInfo.IsFlashSaleValid = true
					break
				}
			}
		}
	}
	return
}

func isFlashSaleValid(traceInfo *traceinfo.TraceInfo, flashSaleDiscount *o2oalgo.FlashSaleDishDiscount, currentTimestamp uint64) bool {
	// check 状态
	if flashSaleDiscount.GetStatus() != o2oalgo.FlashSaleDishStatus_FLASH_SALE_DISH_STATUS_ACTIVE {
		if traceInfo.IsDebug {
			traceInfo.AddFilteredDish(flashSaleDiscount.GetId(), traceinfo.FilteredDishByFlashSaleInvalidStatus, fmt.Sprintf("dishId:%d#flashSaleId:%d#status:%d", flashSaleDiscount.GetDishId(), flashSaleDiscount.GetId(), flashSaleDiscount.GetStatus()))
		}
		return false
	}

	// check 时间戳
	timeSlotStartTime := flashSaleDiscount.GetTimeslotStartTime()
	timeSlotEndTime := flashSaleDiscount.GetTimeslotEndTime()
	if currentTimestamp < timeSlotStartTime || currentTimestamp >= timeSlotEndTime {
		if traceInfo.IsDebug {
			traceInfo.AddFilteredDish(flashSaleDiscount.GetId(), traceinfo.FilteredDishByFlashSaleInvalidTimeslot, fmt.Sprintf("dishId:%d#flashSaleId:%d#status:%d#start:%d#end:%d#current:%d", flashSaleDiscount.GetDishId(), flashSaleDiscount.GetId(), flashSaleDiscount.GetStatus(), timeSlotStartTime, timeSlotEndTime, currentTimestamp))
		}
		return false
	}
	return true
}

func DishInfosFillingForFlashSale(ctx context.Context, traceInfo *traceinfo.TraceInfo, flashSaleIds []uint64) (map[uint64]*o2oalgo.FlashSaleDishDiscount, error) {
	if integrate.DataManageServiceClient == nil || len(flashSaleIds) == 0 || decision.IsDishMetaDowngrade(traceInfo) {
		return nil, nil
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingDishFlashSaleRPCOne, time.Since(pt))
	}()

	flashSaleResp, err, partFailedFlag := integrate.DataManageServiceClient.GetFlashSaleDishDiscountWithMetaPool(ctx, flashSaleIds, traceInfo.MetaPool)
	if partFailedFlag {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
	}

	metric_reporter2.ReportDurationAndQPS(time.Since(pt), metric_reporter2.SearchReportTypeRpc, "", "", "DishInfosFillingForFlashSale")
	if err != nil || flashSaleResp.GetRet() != 0 {
		traceInfo.AddErrorToTraceInfo(err)
		logkit.FromContext(ctx).WithError(err).Error("GetDishMeta failed to get flashSale meta info", zap.Any("cost", time.Since(pt)))
		traceInfo.IsDowngradeDataServer = true
	}
	return flashSaleResp.GetMapFlashSaleDishDiscount(), nil
}

func GetFlashSaleInfos(ctx context.Context, traceInfo *traceinfo.TraceInfo, flashSaleIds []uint64) (map[uint64]*o2oalgo.FlashSaleDishDiscount, error) {
	batchSize := 50
	if apollo.SearchApolloCfg.DishFlashSaleCacheBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.DishFlashSaleCacheBatchSize)
	}

	flashSaleLen := len(flashSaleIds)
	totalBatch := (flashSaleLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return nil, nil
	}

	batchResultList := make([]map[uint64]*o2oalgo.FlashSaleDishDiscount, totalBatch)
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "GetFlashSaleInfos", func(params ...interface{}) {
			defer wg.Done()

			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > flashSaleLen {
				end = flashSaleLen
			}
			batchFlashSaleIds := flashSaleIds[begin:end]
			batchFlashSaleRsp, err := DishInfosFillingForFlashSale(ctx, traceInfo, batchFlashSaleIds)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("GetFlashSaleInfos failed", logkit.Uint64s("flashSaleIds", batchFlashSaleIds))
				return
			}
			batchResultList[groupIndex] = batchFlashSaleRsp
		}, i)
	}
	wg.Wait()

	rsp := make(map[uint64]*o2oalgo.FlashSaleDishDiscount, len(flashSaleIds))
	for _, batchFlashSaleMap := range batchResultList {
		for flashSaleId, flashSale := range batchFlashSaleMap {
			rsp[flashSaleId] = flashSale
		}
	}

	return rsp, nil
}
