package rank

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type LTRRanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewLTRRanker() *LTRRanker {
	ltr := &LTRRanker{}
	return ltr
}

func (ltr *LTRRanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ltr.contextFea = ctxFea
	ltr.itemFea = itemFea
	ltr.itemIds = itemIds
}

func (ltr *LTRRanker) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string) error {
	req := &predictor.PredictReq{
		Uid: traceInfo.UserId,
		//Itemids: ltr.itemIds,
		CtxFea: ltr.contextFea,
		Reqid:  traceInfo.TraceRequest.PublishId,
		Models: []string{modelName},
	}
	if ltr.itemIds != nil {
		req.Itemids = ltr.itemIds
	}
	if ltr.itemFea != nil {
		req.ItemFeas = ltr.itemFea
	}

	// 检查是否为diff流量
	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, req, "ltr", true)
		if mockScores != nil && len(mockScores) == nStores {
			for i, s := range stores {
				s.PLtrScore = mockScores[i]
				s.IsMockPLtrScore = 1
			}
		}
		traceInfo.LtrModelInfo.IsPredictSuccess = true
		traceInfo.PredictConfig.LtrModuleName = modelName
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	name := "predict-ltr-" + modelName
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankLTRPredict, time.Since(startTime))
		metric_reporter.ReportClientRequestError(1, name, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RERANK_ERROR)
		}
	}()
	resp, _, err := mlplatform.Predict(ctx, req, false)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", name)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", traceInfo.PredictConfig.CvrModuleName))
		return err
	}
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		rspStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("doPredict", logkit.String("modelName", modelName), logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
	}

	errCode = "0"
	scoreInfo := resp.GetModScoresInfo()[modelName]
	var scores *predictor.ScoreList
	if scoreInfo != nil {
		scores = scoreInfo.Scores["ltr"]
		if scores != nil && scores.Scores != nil {
			if len(scores.Scores) != nStores {
				metric_reporter.ReportClientRequestError(1, name, "len")
				logkit.FromContext(ctx).Error("failed to get ltr score", logkit.Int("scoresLen", len(scores.Scores)), logkit.Int("storesLen", nStores))
				return errors.New("predict ltr scoresLen diff")
			}
			for i := 0; i < nStores; i++ {
				stores[i].PLtrScore = scores.Scores[i]
			}
		}
	}
	if len(resp.GetModelInfo()) > 0 {
		traceInfo.LtrModelInfo.RspModelInfo = resp.ModelInfo[0]
	}
	return nil
}
func (ltr *LTRRanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictLtr) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictLtr")
		metric_reporter.ReportClientRequestError(1, "predict_ltr_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_ltr_downgrade", "0")
	if traceInfo.PredictConfig.UseLtr && len(traceInfo.LtrModelInfo.ModelName) > 0 {
		err := ltr.doPredict(ctx, stores, traceInfo, nStores, traceInfo.LtrModelInfo.ModelName)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			// Ltr模型失败,使用 pctr * pcvr 分数进行兜底
			for _, store := range stores {
				store.PLtrScore = store.PCtrScore * store.PCvrScore
			}
		}
	}
	return nil
}

// LTR 模型依赖pctr/pcvr 作为特征，需要在ctr预估预估之后.
// LTR 模型依赖relevance 分数，需要RelevanceDefaultFilling之后执行,且在CalculateReRankScore之前
func CalculateAfterLtrAndRankByRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedPredictAndReRankScore(traceInfo) == false {
		return stores
	}
	nStores := len(stores)
	if env.GetCID() != cid.VN {
		stores.CalculateParam(ctx, traceInfo)
	}
	traceInfo.LtrModelInfo = buildModelInfo(traceInfo, LtrPredictor)

	itemFeas, itemIds := buildItemFeatureForLtr(ctx, traceInfo, stores, nStores)
	ltrPredict := NewLTRRanker()
	ltrPredict.setFeatures(traceInfo.ContextFeatureBytes, itemFeas, itemIds)
	ltrPredict.doRank(ctx, stores, traceInfo, nStores)
	pct := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankLTRCalculate, time.Since(pct))
	}()

	stores.CalculateReRankScore(ctx, traceInfo, false)

	return stores
}
