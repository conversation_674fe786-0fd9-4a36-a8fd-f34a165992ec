package rank

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/pcfactor"

	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/gogo/protobuf/proto"
)

type CtrRanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewCtrRanker() *CtrRanker {
	return &CtrRanker{}
}

func (ranker *CtrRanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ranker.contextFea = ctxFea
	ranker.itemFea = itemFea
	ranker.itemIds = itemIds
}

func (ranker *CtrRanker) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string, bPredict bool) error {
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  ranker.itemIds,
		CtxFea:   ranker.contextFea,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{modelName},
		UserFea:  nil,
		ItemFeas: ranker.itemFea,
	}

	// 检查是否为diff流量
	if decision.IsMockModelScore(ctx, traceInfo) {
		if bPredict {
			mockScores := mockPredictScoreWhenDiff(ctx, predictReq, "ctr", false)
			if mockScores != nil && len(mockScores) == nStores {
				for i, s := range stores {
					s.PCtrScore = mockScores[i]
					s.IsMockPCtrScore = 1
				}
			}
		}
		traceInfo.CtrModelInfo.IsPredictSuccess = true
		traceInfo.PredictConfig.CtrModuleName = modelName
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictCtr, time.Since(startTime))
		metric_reporter.ReportClientRequestError(1, "predict-ctr-"+modelName, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		}
	}()
	resp, isSpex, err := mlplatform.Predict(ctx, predictReq, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-ctr-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-ctr-"+modelName)
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(predictReq)
		respStr, _ := json.Marshal(resp)
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ctr predict response", logkit.Any("request", string(reqStr)), logkit.Any("response", string(respStr)))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict ctr", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", modelName))
		return err
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict ctr", logkit.Any("rsp is ", "nil"))
		return errors.New("predict ctr resp is nil")
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict ctr", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	if bPredict {
		if traceInfo.PredictConfig.IsCtrCvrUnified == false {
			scores := resp.GetModScores()["CTR"].GetScores()
			if len(scores) != nStores {
				metric_reporter.ReportClientRequestError(1, "predict-ctr", "len")
				logkit.FromContext(ctx).Error("failed to get ctr score", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", nStores))
				return errors.New("predict ctr scoresLen diff")
			}
			for i := 0; i < nStores; i++ {
				stores[i].PCtrScore = scores[i]
			}
		} else {
			// 短期方案，后续单独优化
			modScores := resp.GetModScoresInfo()[modelName]
			if len(modScores.GetScores()) == 0 {
				metric_reporter.ReportClientRequestError(1, "predict-ctr", "len")
				logkit.FromContext(ctx).Error("failed to get ctr score", logkit.Int("scoresLen", len(modScores.GetScores())), logkit.Int("storesLen", len(stores)))
				return errors.New("predict ctr scores empty")
			}
			if len(traceInfo.PredictConfig.UnifiedModuleCtrKey) > 0 {
				if scoreMap, ok := modScores.GetScores()[traceInfo.PredictConfig.UnifiedModuleCtrKey]; ok {
					scores := scoreMap.GetScores()
					if len(scores) != nStores {
						metric_reporter.ReportClientRequestError(1, "predict-ctr", "len")
						logkit.FromContext(ctx).Error("failed to get ctr score", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", nStores))
						return errors.New("predict ctr scoresLen diff")
					}
					for i := 0; i < nStores; i++ {
						stores[i].PCtrScore = scores[i]
					}
					traceInfo.CtrModelInfo.IsPredictSuccess = true
				} else {
					logkit.FromContext(ctx).Error("UnifiedModuleCtrKey not found in modScores", logkit.String("key", traceInfo.PredictConfig.UnifiedModuleCtrKey))
				}
			}
			if len(traceInfo.PredictConfig.UnifiedModuleCvrKey) > 0 {
				if scoreMap, ok := modScores.GetScores()[traceInfo.PredictConfig.UnifiedModuleCvrKey]; ok {
					scores := scoreMap.GetScores()
					if len(scores) != nStores {
						metric_reporter.ReportClientRequestError(1, "predict-ctr", "len")
						logkit.FromContext(ctx).Error("failed to get cvr score", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", nStores))
						return errors.New("predict ctr scoresLen diff")
					}
					for i := 0; i < nStores; i++ {
						stores[i].PCvrScore = scores[i]
					}
					traceInfo.CvrModelInfo.IsPredictSuccess = true
				} else {
					logkit.FromContext(ctx).Error("UnifiedModuleCvrKey not found in modScores", logkit.String("key", traceInfo.PredictConfig.UnifiedModuleCvrKey))
				}
			}
		}
	}
	errCode = "0"
	traceInfo.CtrModelInfo.IsPredictSuccess = true
	traceInfo.PredictConfig.CtrModuleName = modelName
	if isSpex == true {
		traceInfo.PredictConfig.AckIpInstance = resp.GetIpaddr()
	} else {
		traceInfo.PredictConfig.AckIpAddr = resp.GetIpaddr()
	}
	return nil
}

func DoPredictAck(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, normalStoreMap map[uint64]*model.StoreInfo) {
	// diff 流量不走ack
	if traceInfo.SearchDebugReq.GetTrafficFlag() == foodalgo_search.SearchDebugReq_Diff {
		return
	}
	if (len(traceInfo.PredictConfig.AckIpAddr) == 0 && len(traceInfo.PredictConfig.AckIpInstance) == 0) || len(stores) == 0 {
		return
	}
	if traceInfo.IsSkipModel {
		logkit.Debug("skip ack")
		return
	}
	if env.GetCID() == cid.VN {
		if traceInfo.HandlerType != traceinfo.HandlerTypeSearchFoodTotalNum && traceInfo.HandlerType != traceinfo.HandlerTypeSearchMartTotalNum {
			PredictAckForVN(ctx, traceInfo, stores)
		}
	} else {
		err := PredictAckForID(ctx, stores, traceInfo, normalStoreMap)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("PredictAckForID failed")
			return
		}
	}
}

func (ranker *CtrRanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictCtr) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictCtr")
		metric_reporter.ReportClientRequestError(1, "predict_ctr_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_ctr_downgrade", "0")
	if len(traceInfo.CtrModelInfo.ModelName) > 0 {
		err := ranker.doPredict(ctx, stores, traceInfo, nStores, traceInfo.CtrModelInfo.ModelName, traceInfo.CtrModelInfo.IsNeedPredict)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			return err
		}
	}
	return nil
}

func PredictAckForVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	logkit.FromContext(ctx).Info("do ack", logkit.Any("ip", traceInfo.PredictConfig.AckIpAddr), logkit.Any("spex ip", traceInfo.PredictConfig.AckIpInstance))
	itemIds := make([]uint64, 0, 150)
	var res model.StoreInfos
	ackNum := 500
	if apollo.SearchApolloCfg.PredictAckNum > 0 {
		ackNum = apollo.SearchApolloCfg.PredictAckNum
	}
	if len(stores) > ackNum {
		res = stores[:ackNum]
	} else {
		res = stores
	}

	reRankScoreMap := make(map[string]*predictor.ScoreL)
	modScoresInfoMap := make(map[string]*predictor.ScoresInfo)
	reRankScore := make([]float64, 0)
	pCvrScores := make([]float64, 0)
	pCoarseScores := make([]float64, 0)
	pRelevanceScores := make([]float64, 0)
	itemFeatures := make([][]byte, 0)
	recallTypeMap := apollo.GetRecallTypeMap()
	for _, store := range res {
		itemIds = append(itemIds, store.StoreId)
		reRankScore = append(reRankScore, store.ReRankScore)
		pCvrScores = append(pCvrScores, store.PCvrScore)
		pCoarseScores = append(pCoarseScores, store.CoarseRankScore)
		pRelevanceScores = append(pRelevanceScores, store.PRelevanceScore)

		// ack 全部的dishInfos
		if len(store.DishInfos) > 0 {
			dishIds := make([]int64, 0, len(store.DishInfos))
			dishScores := make([]float32, 0, len(store.DishInfos))
			dishRecallTypes := make([]string, 0, len(store.DishInfos))
			for _, dish := range store.DishInfos {
				dishIds = append(dishIds, int64(dish.DishId))
				dishScores = append(dishScores, float32(dish.Score))
				recallTypeIntList := getRecallTypeInt(ctx, dish, recallTypeMap)
				dishRecallTypes = append(dishRecallTypes, strings.Join(recallTypeIntList, "|"))
			}
			itemFeature := &food.AfterItemFeature{
				CDishListingIdList:         dishIds,
				CDishListingRecallTypeList: dishRecallTypes,
				CDishListingScoreList:      dishScores,
			}
			data, _ := proto.Marshal(itemFeature)
			itemFeatures = append(itemFeatures, data)
		}
	}

	modelScore := make(map[string]*predictor.ScoreL)
	modNameVersion := make(map[string]string)
	info := traceInfo.CvrModelInfo.RspModelInfo
	if len(info.GetModName()) > 0 {
		modNameVersion[info.GetModName()] = info.GetModVersion()
		modelScore[info.GetModName()] = &predictor.ScoreL{Scores: pCvrScores}
	}

	if len(traceInfo.RelModelInfo.ModelName) > 0 {
		relScoreTemp := make(map[string]*predictor.ScoreList)
		relScoreTemp["p_relevance_scores"] = &predictor.ScoreList{Scores: pRelevanceScores}
		modScoresInfoMap[traceInfo.RelModelInfo.ModelName] = &predictor.ScoresInfo{
			Scores: relScoreTemp,
		}
	}

	reRankScoreMap["rerank_score"] = &predictor.ScoreL{Scores: reRankScore}
	if len(traceInfo.AckDumpReRankScoreSuffix) > 0 {
		reRankScoreMap["rerank_score_"+traceInfo.AckDumpReRankScoreSuffix] = &predictor.ScoreL{Scores: reRankScore}
	}

	if len(traceInfo.CoarseUserEmbedding) > 0 && len(traceInfo.CoarseModelName) > 0 {
		userEmbMapTemp := make(map[string]*predictor.ScoreList)
		userEmbMapTemp["user_embedding"] = &predictor.ScoreList{Scores: traceInfo.CoarseUserEmbedding}

		coarseScoreTemp := make(map[string]*predictor.ScoreList)
		coarseScoreTemp["coarse_rank_score"] = &predictor.ScoreList{Scores: pCoarseScores}

		modScoresInfoMap[traceInfo.CoarseModelName] = &predictor.ScoresInfo{
			Scores:    coarseScoreTemp,
			CtxScores: userEmbMapTemp,
			Version:   "",
		}
	}

	abTestGroup := strings.Split(traceInfo.ABTestGroup.GetABTestString(), ",")
	req := &predictor.PreAckReq{
		Reqid:           traceInfo.TraceRequest.PublishId,
		Itemids:         itemIds,
		AbGroup:         abTestGroup,
		ModelName:       traceInfo.PredictConfig.CtrModuleName,
		ModScores:       modelScore,
		ModFusionScores: reRankScoreMap,
		ModNameVersion:  modNameVersion,
		ModScoresInfo:   modScoresInfoMap,
		ItemFeas:        itemFeatures,
	}
	startTime := time.Now()
	rsp, err := mlplatform.PredictAck(ctx, req, traceInfo.PredictConfig.AckIpAddr, traceInfo.PredictConfig.AckIpInstance, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict_ack"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict_ack")
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ack predict", logkit.Any("request", req), logkit.Any("response", rsp))
	}
	if err != nil {
		metric_reporter.ReportClientRequestError(1, "predict_ack", "failed")
		logkit.FromContext(ctx).Error("failed to predict ctr ack", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId))
		return
	}
	if rsp == nil || rsp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict ctr ack", logkit.Any("CtrModuleName", traceInfo.PredictConfig.CtrModuleName), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId), logkit.Any("rsp", rsp))
	}
	metric_reporter.ReportClientRequestError(1, "predict_ack", "0")
}

func PredictAckForID(ctx context.Context, searchStoresResult model.StoreInfos,
	traceInfo *traceinfo.TraceInfo, normalStoreMap map[uint64]*model.StoreInfo) error {
	var res model.StoreInfos
	ackNum := 150
	if apollo.SearchApolloCfg.PredictAckNum != 0 {
		ackNum = apollo.SearchApolloCfg.PredictAckNum
		if len(searchStoresResult) > ackNum {
			res = searchStoresResult[:ackNum]
		} else {
			res = searchStoresResult
		}
	}
	itemIds := make([]uint64, 0)
	ueScore := make([]float64, 0)
	ueRatioScore := make([]float64, 0)
	reRankScore := make([]float64, 0)
	pUeScore := make([]float64, 0)
	pCvrScore := make([]float64, 0)
	ltrRerankScore := make([]float64, 0)
	modelScore := make(map[string]*predictor.ScoreL)
	reRankScoreMap := make(map[string]*predictor.ScoreL)
	reRankScoreMapTemp := make(map[string]*predictor.ScoreList)
	reRankScoreMapV2 := make(map[string]*predictor.ScoresInfo)
	itemFeatures := make([][]byte, 0)
	modNameVersion := make(map[string]string)
	pCoarseScores := make([]float64, 0)
	pRelevanceScores := make([]float64, 0)
	recallTypeMap := apollo.GetRecallTypeMap()
	for storeIdx := 0; storeIdx < len(res); storeIdx++ {
		s := res[storeIdx]
		if normalStoreMap != nil {
			normalStore, ok := normalStoreMap[s.StoreId]
			// 广告使用 normal 结果中的分数进行 dump.
			if s.BusinessType == foodalgo_search.BusinessType_Ads && ok {
				ReplaceAckFeature(s, normalStore)
			}
		}
		isIntervention := 0
		if (s.StoreInterventionWithMerchantIDRecall == 1 || s.StoreInterventionRecall == 1) && s.IsInterForSort == 1 {
			isIntervention = 1
		}
		itemIds = append(itemIds, s.StoreId)
		ueScore = append(ueScore, s.UE)
		pUeScore = append(pUeScore, s.PUEScore)
		pCvrScore = append(pCvrScore, s.PCvrScore)
		ueRatioScore = append(ueRatioScore, s.UERadio)
		pCoarseScores = append(pCoarseScores, s.CoarseRankScore)
		ltrRerankScore = append(ltrRerankScore, s.PLtrScore)
		pRelevanceScores = append(pRelevanceScores, s.PRelevanceScore)
		// 判断是否等于 0
		if traceInfo.IsDebug && math.Abs(s.ReRankScore) < 1e-9 {
			logkit.FromContext(ctx).Info("rerank score is 0", zap.Any("storeInfo", s))
		}
		reRankScore = append(reRankScore, s.ReRankScore)
		itemFeature := &food.AfterItemFeature{
			CIsInterTop:          int64(isIntervention),
			CRelevanceScore:      float32(s.RelevanceScore),
			CStoreBeforeInterPos: s.InterBeforePos,
			CStoreAfterInterPos:  s.InterAfterPos,
		}

		// ack 全部的dishInfos
		if len(s.DishInfos) > 0 {
			dishIds := make([]int64, 0, len(s.DishInfos))
			dishRecallTypes := make([]string, 0, len(s.DishInfos))
			dishScores := make([]float32, 0, len(s.DishInfos))
			for _, dish := range s.DishInfos {
				dishIds = append(dishIds, int64(dish.DishId))
				dishScores = append(dishScores, float32(dish.Score))
				recallTypeIntList := getRecallTypeInt(ctx, dish, recallTypeMap)
				dishRecallTypes = append(dishRecallTypes, strings.Join(recallTypeIntList, "|"))
			}
			itemFeature.CDishListingIdList = dishIds
			itemFeature.CDishListingRecallTypeList = dishRecallTypes
			itemFeature.CDishListingScoreList = dishScores
		}

		if traceInfo.IsDebug && storeIdx < 20 {
			logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ack predict item", logkit.Uint64("store_id", s.StoreId), logkit.Any("item feature", itemFeature))
		}
		data, _ := proto.Marshal(itemFeature)
		itemFeatures = append(itemFeatures, data)
	}

	if len(traceInfo.RelModelInfo.ModelName) > 0 {
		relScoreTemp := make(map[string]*predictor.ScoreList)
		relScoreTemp["p_relevance_scores"] = &predictor.ScoreList{Scores: pRelevanceScores}
		reRankScoreMapV2[traceInfo.RelModelInfo.ModelName] = &predictor.ScoresInfo{
			Scores: relScoreTemp,
		}
	}

	reRankScoreMap["rerank_score"] = &predictor.ScoreL{Scores: reRankScore}
	reRankScoreMapTemp["rerank_score"] = &predictor.ScoreList{Scores: reRankScore}
	if len(traceInfo.AckDumpReRankScoreSuffix) > 0 {
		reRankScoreMap["rerank_score_"+traceInfo.AckDumpReRankScoreSuffix] = &predictor.ScoreL{
			Scores: reRankScore,
		}
		reRankScoreMapTemp["rerank_score_"+traceInfo.AckDumpReRankScoreSuffix] = &predictor.ScoreList{
			Scores: reRankScore,
		}
	}
	reRankScoreMapV2["fusion"] = &predictor.ScoresInfo{
		Scores:  reRankScoreMapTemp,
		Version: "",
	}
	if len(traceInfo.CoarseUserEmbedding) > 0 && len(traceInfo.CoarseModelName) > 0 {
		userEmbMapTemp := make(map[string]*predictor.ScoreList)
		userEmbMapTemp["user_embedding"] = &predictor.ScoreList{Scores: traceInfo.CoarseUserEmbedding}

		coarseScoreTemp := make(map[string]*predictor.ScoreList)
		coarseScoreTemp["coarse_rank_score"] = &predictor.ScoreList{Scores: pCoarseScores}

		reRankScoreMapV2[traceInfo.CoarseModelName] = &predictor.ScoresInfo{
			Scores:    coarseScoreTemp,
			CtxScores: userEmbMapTemp,
			Version:   "",
		}
	}
	// ID 地区需要dump cvr, ue 预估分数
	cvrInfo := traceInfo.CvrModelInfo.RspModelInfo
	if len(cvrInfo.GetModName()) > 0 {
		modNameVersion[cvrInfo.GetModName()] = cvrInfo.GetModVersion()
		modelScore[cvrInfo.GetModName()] = &predictor.ScoreL{Scores: pCvrScore}
	}
	ueInfo := traceInfo.UEModelInfo.RspModelInfo
	if len(ueInfo.GetModName()) > 0 {
		modNameVersion[ueInfo.GetModName()] = ueInfo.GetModVersion()
		modelScore[ueInfo.GetModName()] = &predictor.ScoreL{Scores: pUeScore}
	}
	ltrInfo := traceInfo.LtrModelInfo.RspModelInfo
	if ltrInfo != nil && len(ltrInfo.GetModName()) > 0 {
		modNameVersion[ltrInfo.GetModName()] = ltrInfo.GetModVersion()
		modelScore[ltrInfo.GetModName()] = &predictor.ScoreL{Scores: ltrRerankScore}
	}
	reRankScoreMap["UE"] = &predictor.ScoreL{Scores: ueScore}
	reRankScoreMap["UERation"] = &predictor.ScoreL{Scores: ueRatioScore}
	if traceInfo.PredictConfig != nil && traceInfo.PredictConfig.DyFactorList != nil && len(traceInfo.PredictConfig.DyFactorList) == 6 {
		reRankScoreMapV2[traceInfo.DFModelInfo.ModelName] = &predictor.ScoresInfo{}
		reRankScoreMapV2[traceInfo.DFModelInfo.ModelName].CtxScores = map[string]*predictor.ScoreList{}
		reRankScoreMapV2[traceInfo.DFModelInfo.ModelName].CtxScores["ltr"] = &predictor.ScoreList{}
		reRankScoreMapV2[traceInfo.DFModelInfo.ModelName].CtxScores["ltr"].Scores = traceInfo.PredictConfig.DyFactorList
		reRankScoreMapV2[traceInfo.DFModelInfo.ModelName].CtxScores["ltr"].Len = 6
	}
	abTestGroup := strings.Split(traceInfo.ABTestGroup.GetABTestString(), ",") // todo 验证数据正确性
	reqID := traceInfo.TraceRequest.PublishId
	if len(reqID) == 0 {
		logkit.FromContext(ctx).Info("predict debug log:ack predict, reqID is empty, skip ack", zap.String("reqID", reqID))
		reqID = "default"
		return nil
	}
	if traceInfo.PredictConfig.UsePcFactor {
		pcFactor := pcfactor.PcFactorDict.GetPcFactor(traceInfo.PredictConfig.AbTestVal)
		pcInfo := fmt.Sprintf("pc_factor_info=%s:%s:%f:%f:%f", pcFactor.VersionId, traceInfo.PredictConfig.AbTestVal, pcFactor.W3, pcFactor.W4, pcFactor.W5)
		abTestGroup = append(abTestGroup, pcInfo)
	}
	req := &predictor.PreAckReq{
		Reqid:           reqID,
		Itemids:         itemIds,
		AbGroup:         abTestGroup,
		ModNameVersion:  modNameVersion,
		ModFusionScores: reRankScoreMap,
		ModScores:       modelScore,
		ItemFeas:        itemFeatures,
		ModScoresInfo:   reRankScoreMapV2,
		ModelName:       traceInfo.PredictConfig.CtrModuleName,
	}
	startTime := time.Now()
	var resp *predictor.PreAckRsp
	var err error
	resp, err = mlplatform.PredictAck(ctx, req, traceInfo.PredictConfig.AckIpAddr, traceInfo.PredictConfig.AckIpInstance, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict_ack"), zap.String("cost", time.Since(startTime).String()))
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		respStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("predict debug log:ack", zap.String("req", string(reqStr)), zap.String("resp", string(respStr)),
			zap.String("AckIpAddr", traceInfo.PredictConfig.AckIpAddr), zap.String("AckIpInstance", traceInfo.PredictConfig.AckIpInstance))
	}
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict_ack")
	if err != nil {
		logkit.FromContext(ctx).Error("predict debug log:failed to predict ctr ack", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId))
		metric_reporter.ReportClientRequestError(1, "predict_ack", "failed")
		return err
	}
	metric_reporter.ReportClientRequestError(1, "predict_ack", "0")
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("predict debug log:failed to predict ack", logkit.Any("CtrModuleName", traceInfo.PredictConfig.CtrModuleName), logkit.Any("publish_id", traceInfo.TraceRequest.PublishId), logkit.Any("resp", resp))
		return errors.New(resp.GetErrdesc())
	}
	return nil
}

func ReplaceAckFeature(ads, normal *model.StoreInfo) {
	ads.PCvrScore = normal.PCvrScore
	ads.UE = normal.UE
	ads.UERadio = normal.UERadio
	ads.ReRankScore = normal.ReRankScore
	ads.RelevanceScore = normal.RelevanceScore
	ads.InterBeforePos = normal.InterBeforePos
	ads.InterAfterPos = normal.InterAfterPos
	ads.StoreInterventionWithMerchantIDRecall = normal.StoreInterventionWithMerchantIDRecall
	ads.StoreInterventionRecall = normal.StoreInterventionRecall
	ads.IsInterForSort = normal.IsInterForSort
}

func getRecallTypeInt(ctx context.Context, dish *model.DishInfo, recallTypeMap map[string]int32) []string {
	recallTypeIntList := make([]string, 0)
	for _, recallTypeStr := range dish.DishRecallTypes {
		val, isOk := recallTypeMap[recallTypeStr]
		if isOk {
			recallTypeIntList = append(recallTypeIntList, fmt.Sprintf("%d", val))
		} else {
			logkit.FromContext(ctx).Error("PredictAck failed to find RecallTypeInt", logkit.String("recallType", recallTypeStr))
		}
	}
	return recallTypeIntList
}
