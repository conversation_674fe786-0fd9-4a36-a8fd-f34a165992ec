package parse

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/jinzhu/copier"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GetStoreRecallConfigByPipelineType(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallDomain string, dataSource string) *apollo.RecallConfig {
	apollo.SearchRecallConf.SearchServerApolloConfigLock.RLock()
	defer apollo.SearchRecallConf.SearchServerApolloConfigLock.RUnlock()

	// 初始化一个空的召回配置
	rc := apollo.RecallConfig{
		StoreRecalls: make([]*apollo.StoreRecallConfig, 0),
		Common:       &apollo.RecallCommon{},
	}
	rc.StoreRecalls = getHitRecallsByPipelineType(ctx, traceInfo, apollo.SearchRecallConf.RecallConfig.StoreRecalls, apollo.SearchRecallConf.RecallConfig.Common, recallDomain, dataSource)
	if len(rc.StoreRecalls) > 0 {
		if apollo.SearchRecallConf.RecallConfig.Common != nil {
			err := copier.Copy(&rc.Common, &apollo.SearchRecallConf.RecallConfig.Common)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration GetStoreRecallConfigByPipelineType Failed to clone", logkit.Any("recallDomain", recallDomain), logkit.String("dataSource", dataSource))
				return nil
			}
		}
	}
	return &rc
}

func GetDishRecallConfigByPipelineType(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallDomain string, dataSource string) *apollo.RecallConfig {
	apollo.SearchRecallConf.SearchServerApolloConfigLock.RLock()
	defer apollo.SearchRecallConf.SearchServerApolloConfigLock.RUnlock()

	// 初始化一个空的召回配置
	rc := apollo.RecallConfig{
		DishRecalls: make([]*apollo.StoreRecallConfig, 0),
		DishCommon:  &apollo.RecallCommon{},
	}
	rc.DishRecalls = getHitRecallsByPipelineType(ctx, traceInfo, apollo.SearchRecallConf.RecallConfig.DishRecalls, apollo.SearchRecallConf.RecallConfig.DishCommon, recallDomain, dataSource)
	if len(rc.DishRecalls) > 0 {
		if apollo.SearchRecallConf.RecallConfig.DishCommon != nil {
			err := copier.Copy(&rc.DishCommon, &apollo.SearchRecallConf.RecallConfig.DishCommon)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration GetDishRecallConfigByPipelineType Failed to clone", logkit.Any("recallDomain", recallDomain), logkit.String("dataSource", dataSource))
				return nil
			}
		}
	}
	return &rc
}

func GetDishRecallConfig(ctx context.Context, traceInfo *traceinfo.TraceInfo) *apollo.RecallConfig {
	apollo.SearchRecallConf.SearchServerApolloConfigLock.RLock()
	defer apollo.SearchRecallConf.SearchServerApolloConfigLock.RUnlock()

	// 初始化一个空的召回配置
	rc := apollo.RecallConfig{
		DishRecalls: make([]*apollo.StoreRecallConfig, 0),
		DishCommon:  &apollo.RecallCommon{},
	}
	rc.DishRecalls = getHitRecalls(ctx, traceInfo, apollo.SearchRecallConf.RecallConfig.DishRecalls, traceinfo.FoodSchRecall, recallconstant.RecallDomainDish)
	if len(rc.DishRecalls) > 0 {
		if apollo.SearchRecallConf.RecallConfig.DishCommon != nil {
			err := copier.Copy(&rc.DishCommon, &apollo.SearchRecallConf.RecallConfig.DishCommon)
			if err != nil {
				logkit.FromContext(ctx).Error("RecallConfiguration GetDishRecallConfig Failed to clone", logkit.Any("err", err))
				return nil
			}
		}
	} else {
		logkit.FromContext(ctx).Error("RecallConfiguration GetDishRecallConfig empty configs")
		return nil
	}
	return &rc
}

func getHitRecalls(ctx context.Context, traceInfo *traceinfo.TraceInfo, originRecalls []*apollo.StoreRecallConfig, defaultRecallKey string, recallDomain string) []*apollo.StoreRecallConfig {

	if len(originRecalls) == 0 {
		logkit.FromContext(ctx).Error("RecallConfiguration originRecalls is empty", logkit.String("defaultRecallKey", defaultRecallKey))
		return nil
	}

	hitRecalls := make([]*apollo.StoreRecallConfig, 0)
	for _, recallConfig := range originRecalls {
		recallStatName := fmt.Sprintf("%s_%s", recallConfig.RecallName, recallConfig.RecallId)
		if apollo.SearchApolloCfg.AbtestParamsSwitch.RecallConfigUseParamsPlatform {
			if abtest.GetOneRecallConfig(recallConfig.RecallName, traceInfo.AbParamClient) || abtest.GetOneRecallConfigV2(recallConfig.RecallName, traceInfo.AbParamClient) || abtest.GetOneRecallConfigWithDataSourceAndDomain(recallConfig.RecallName, recallConfig.DataSource, recallDomain, traceInfo.AbParamClient) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration getHitRecalls key-value hit group from abtest config platform", logkit.String("recallName", recallConfig.RecallName))
				hitRecalls = append(hitRecalls, recallConfig)
				if traceInfo.IsDebug {
					if recallDomain == recallconstant.RecallDomainStore {
						traceInfo.AppendRecallsStoreHitAbtest(recallStatName)
					} else if recallDomain == recallconstant.RecallDomainDish {
						traceInfo.AppendRecallsDishHitAbtest(recallStatName)
					}
				}
			} else {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration getHitRecalls key-value not hit group from abtest config platform", logkit.String("recallName", recallConfig.RecallName))
				if traceInfo.IsDebug {
					if recallDomain == recallconstant.RecallDomainStore {
						traceInfo.AppendRecallsStoreNotHitAbtest(recallStatName)
					} else if recallDomain == recallconstant.RecallDomainDish {
						traceInfo.AppendRecallsDishNotHitAbtest(recallStatName)
					}
				}
			}
		} else {
			if traceInfo.ABTestGroup.IsHitAbTestMultiKey(recallConfig.AbKey) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration getHitRecalls key-value hit group", logkit.String("recallName", recallConfig.RecallName))
				hitRecalls = append(hitRecalls, recallConfig)
				if traceInfo.IsDebug {
					if recallDomain == recallconstant.RecallDomainStore {
						traceInfo.AppendRecallsStoreHitAbtest(recallStatName)
					} else if recallDomain == recallconstant.RecallDomainDish {
						traceInfo.AppendRecallsDishHitAbtest(recallStatName)
					}
				}
			} else {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration getHitRecalls key-value not hit group", logkit.String("recallName", recallConfig.RecallName))
				if traceInfo.IsDebug {
					if recallDomain == recallconstant.RecallDomainStore {
						traceInfo.AppendRecallsStoreNotHitAbtest(recallStatName)
					} else if recallDomain == recallconstant.RecallDomainDish {
						traceInfo.AppendRecallsDishNotHitAbtest(recallStatName)
					}
				}
			}
		}
	}
	return hitRecalls
}

func getHitRecallsByPipelineType(ctx context.Context, traceInfo *traceinfo.TraceInfo, originRecalls []*apollo.StoreRecallConfig, recallCommon *apollo.RecallCommon, recallDomain string, dataSource string) []*apollo.StoreRecallConfig {

	if len(originRecalls) == 0 {
		logkit.FromContext(ctx).Error("RecallConfiguration getHitRecallsByPipelineType originRecalls is empty", logkit.Any("recallDomain", recallDomain), logkit.String("dataSource", dataSource))
		return nil
	}

	currentPipelineType := traceInfo.PipelineType.String()

	hitRecalls := make([]*apollo.StoreRecallConfig, 0)
	for _, recallConfig := range originRecalls {
		// 判断数据源
		if recallConfig.DataSource != dataSource {
			continue
		}
		// 对应的数据源选定后，不能为空
		if recallConfig.DataSource == recallconstant.RecallDataSourceVectorEngine && recallConfig.VectorEngineRecallConfig == nil {
			logkit.FromContext(ctx).Error("RecallConfiguration getHitRecallsByPipelineType VectorEngineRecallConfig is nil", logkit.String("recallName", recallConfig.RecallName))
			continue
		}
		if recallConfig.DataSource == recallconstant.RecallDataSourceFS && recallConfig.FsRecallConfig == nil {
			logkit.FromContext(ctx).Error("RecallConfiguration getHitRecallsByPipelineType FsRecallConfig is nil", logkit.String("recallName", recallConfig.RecallName))
			continue
		}

		// 判断场景
		var findFlag bool
		if recallConfig.PipelineTypes != nil {
			for _, pt := range recallConfig.PipelineTypes {
				if pt == currentPipelineType {
					findFlag = true
					break
				}
			}
		}
		if !findFlag {
			continue
		}

		if abtest.GetOneRecallConfig(recallConfig.RecallName, traceInfo.AbParamClient) || abtest.GetOneRecallConfigV2(recallConfig.RecallName, traceInfo.AbParamClient) || abtest.GetOneRecallConfigWithDataSourceAndDomain(recallConfig.RecallName, recallConfig.DataSource, recallDomain, traceInfo.AbParamClient) {
			// 顺便把公共参数的赋值透传
			if recallConfig.RecallTimeoutMs == nil && recallCommon.RecallTimeoutMs > 0 {
				recallConfig.RecallTimeoutMs = proto.Uint64(recallCommon.RecallTimeoutMs)
			}
			if recallConfig.RecallSizeRate == nil && recallCommon.RecallSizeRate > 0 {
				recallConfig.RecallSizeRate = proto.Float64(recallCommon.RecallSizeRate)
			}
			if recallConfig.IsDownGrade == nil && recallCommon.IsDownGrade == true {
				recallConfig.IsDownGrade = proto.Bool(recallCommon.IsDownGrade)
			}

			hitRecalls = append(hitRecalls, recallConfig)
			if traceInfo.IsDebug {
				if recallDomain == recallconstant.RecallDomainStore {
					traceInfo.AppendRecallsStoreHitAbtest(fmt.Sprintf("%s_%s_%s", currentPipelineType, recallConfig.RecallName, recallConfig.RecallId))
				} else if recallDomain == recallconstant.RecallDomainDish {
					traceInfo.AppendRecallsDishHitAbtest(fmt.Sprintf("%s_%s_%s", currentPipelineType, recallConfig.RecallName, recallConfig.RecallId))
				}
			}
		} else {
			if traceInfo.IsDebug {
				if recallDomain == recallconstant.RecallDomainStore {
					traceInfo.AppendRecallsStoreNotHitAbtest(fmt.Sprintf("%s_%s_%s", currentPipelineType, recallConfig.RecallName, recallConfig.RecallId))
				} else if recallDomain == recallconstant.RecallDomainDish {
					traceInfo.AppendRecallsDishNotHitAbtest(fmt.Sprintf("%s_%s_%s", currentPipelineType, recallConfig.RecallName, recallConfig.RecallId))
				}
			}
		}
	}
	return hitRecalls
}

func DoFilterCondition(ctx context.Context, traceInfo *traceinfo.TraceInfo, recalls []*apollo.StoreRecallConfig, recallDomain string) []*apollo.StoreRecallConfig {
	var afterConditionRecalls []*apollo.StoreRecallConfig
	if len(recalls) == 0 {
		return nil
	}

	for _, config := range recalls {
		if config.RecallCondition == nil || len(*config.RecallCondition) == 0 {
			afterConditionRecalls = append(afterConditionRecalls, config)
			continue
		}

		isRecall, err := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, config.RecallCondition)
		if err != nil {
			logkit.FromContext(ctx).Error("RecallConfiguration DoFilterCondition failed", logkit.String("recallName", config.RecallName), logkit.Any("condition", config.RecallCondition))
			continue
		}
		if isRecall == false {
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("RecallConfiguration DoFilterCondition false", logkit.String("recallName", config.RecallName), logkit.Any("condition", config.RecallCondition))
				if recallDomain == recallconstant.RecallDomainStore {
					traceInfo.AppendRecallsStoreNotHitAbtest(fmt.Sprintf("%s_%s_%s", traceInfo.PipelineType.String(), config.RecallName, config.RecallId))
				}
			}
			continue
		}

		afterConditionRecalls = append(afterConditionRecalls, config)
	}
	return afterConditionRecalls
}
