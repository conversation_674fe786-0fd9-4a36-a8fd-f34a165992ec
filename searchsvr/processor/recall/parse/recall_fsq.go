package parse

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GenFsqQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, fsqConf *apollo.FunctionScoreConf) (*elastic.FunctionScoreQuery, error) {
	if fsqConf == nil {
		return nil, nil
	}

	fsq := elastic.NewFunctionScoreQuery()
	isHit := false
	for _, queryItem := range fsqConf.Queries {
		// condition 判断
		if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &queryItem.Condition); !isOk {
			continue
		}

		// 判断 abTest
		if !isHitAbTest(ctx, traceInfo, queryItem.AbKey) {
			continue
		}

		// 特殊逻辑：处理直接加分的特殊情况
		if queryItem.MatchType == MatchTypeScoreFunc {
			fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(*queryItem.Weight))
			isHit = true
			continue
		}

		// 每个 confItem 都可能解析出来多个 query
		tmpQuery := GenQueryList(ctx, traceInfo, recallConfig, &queryItem)
		if tmpQuery != nil && len(tmpQuery) > 0 {
			for _, q := range tmpQuery {
				if queryItem.Weight == nil {
					logkit.FromContext(ctx).Error("RecallConfiguration fsq without weight", logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("queryItem", queryItem))
				} else {
					fsq = fsq.Add(q, elastic.NewWeightFactorFunction(*queryItem.Weight))
					isHit = true
				}
			}
		}
	}

	if isHit {
		fsq.ScoreMode(fsqConf.ScoreMode).BoostMode(fsqConf.BoostMode)
		return fsq, nil
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration fsq gen failed, all query empty", logkit.String("recallName", recallConfig.RecallLogName))
	return nil, nil
}
