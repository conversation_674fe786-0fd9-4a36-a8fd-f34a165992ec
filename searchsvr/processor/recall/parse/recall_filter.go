package parse

import (
	"context"
	"encoding/json"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GenFilterQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, outerFilterConf *apollo.OuterFilterConf) ([]elastic.Query, error) {

	if outerFilterConf == nil || len(outerFilterConf.FilterConf) == 0 {
		return nil, nil
	}
	filters := doGetFilters(ctx, traceInfo, recallConfig, outerFilterConf.FilterConf)

	// 嵌套在 bool 中
	if outerFilterConf.IsNestFilterByBool {
		filter := elastic.NewBoolQuery().Must(filters...)
		return []elastic.Query{filter}, nil
	}
	if len(filters) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenFilterQuery gen filters failed, all filter empty", logkit.String("recallName", recallConfig.RecallLogName))
		return nil, nil
	}
	return filters, nil
}

func GenFilterQueryByRecallCommon(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, recallCommon *apollo.RecallCommon) ([]elastic.Query, error) {

	if len(recallCommon.CommonFilters) == 0 {
		return nil, nil
	}

	filters := doGetFilters(ctx, traceInfo, recallConfig, recallCommon.CommonFilters)
	if len(filters) == 0 {
		return nil, nil
	}
	return filters, nil
}

func MergeFilters(ctx context.Context, filters []elastic.Query, commonFilters []elastic.Query) []elastic.Query {
	if len(commonFilters) == 0 {
		return filters
	}
	if len(filters) == 0 {
		return commonFilters
	}

	uniqueFilters := make([]elastic.Query, 0)
	seen := make(map[string]bool)

	// 去重
	filters = append(filters, commonFilters...)
	for _, filter := range filters {
		source, err := filter.Source()
		if err != nil {
			logkit.FromContext(ctx).Error("RecallConfiguration MergeFilters gen source failed", logkit.Any("filter", filter))
			continue
		}
		bytes, err := json.Marshal(source)
		if err != nil {
			logkit.FromContext(ctx).Error("RecallConfiguration MergeFilters gen source to json failed", logkit.Any("filter", filter))
			continue
		}
		str := string(bytes)
		if !seen[str] {
			seen[str] = true
			uniqueFilters = append(uniqueFilters, filter)
		}
	}
	return uniqueFilters
}

func doGetFilters(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, filterConfigs []apollo.RecallQueryBoolItem) []elastic.Query {

	filters := make([]elastic.Query, 0)
	for _, filterItem := range filterConfigs {
		// 判断condition
		if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &filterItem.Condition); !isOk {
			continue
		}
		// 判断 abTest
		if !isHitAbTest(ctx, traceInfo, filterItem.AbKey) {
			continue
		}

		// 每个 confItem 都可能解析出来多个 query
		tmpQuery := GenQueryList(ctx, traceInfo, recallConfig, &filterItem)
		if tmpQuery != nil && len(tmpQuery) > 0 {
			filters = append(filters, tmpQuery...)
		}
	}
	return filters
}
