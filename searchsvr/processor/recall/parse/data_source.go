package parse

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	TypeString         = 1  // string
	TypeStringList     = 2  // string[]
	TypeBool           = 3  // bool
	TypeUint64List     = 4  // uint64[]
	TypeStringListList = 5  // string[][]
	TypeUint32         = 6  //  uint32
	TypeUint32List     = 7  //  []uint32
	TypeUint64         = 8  //  uint64
	TypeInt32          = 9  //  int32
	TypeInt64          = 10 //  int64
	TypeFloat32        = 11 //  float32
	TypeFloat32List    = 12 //  []float32
	TypeFloat64        = 13 //  float64
	TypeFloat64List    = 14 //  []float64
	TypeInt            = 15 //  int
	TypeInt32List      = 16 //  []int32
	TypeInt64List      = 17 //  []int64

	QuerySourceTypeString      = "string"
	QuerySourceTypeBool        = "bool"
	QuerySourceTypeList        = "list"
	QuerySourceTypeStrFromList = "str_from_list"
	QuerySourceTypeListUint32  = "list_uint32"
	QuerySourceTypeListUint64  = "list_uint64"
	QuerySourceTypeListInt32   = "list_int32"
	QuerySourceTypeListInt64   = "list_int64"
	QuerySourceTypeInt         = "int"
	QuerySourceTypeInt32       = "int32"
	QuerySourceTypeInt64       = "int64"
	QuerySourceTypeUint32      = "uint32"
	QuerySourceTypeUint64      = "uint64"
	QuerySourceTypeFloat32     = "float32"
	QuerySourceTypeFloat64     = "float64"
	QuerySourceTypeListFloat32 = "list_float32"
	QuerySourceTypeListFloat64 = "list_float64"
)

// NerResult 定义一个结构用于存储带有 原始词、ASCII 字段的结果
type NerResult struct {
	Original []string
	Ascii    []string
}

func IsDataSourceNil(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, dataResult *traceinfo.DataSourceItem) bool {
	if dataResult == nil || dataResult.Value == nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil", logkit.String("recallName", recallName))
		return true
	}

	nilFlag := false
	switch dataResult.ValueType {
	case TypeString:
		if value, ok := dataResult.Value.(string); ok {
			if len(value) == 0 {
				nilFlag = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil, string",
					logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
			}
		} else {
			nilFlag = true
			logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil tran data failed, string",
				logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		}
	case TypeStringList:
		if value, ok := dataResult.Value.([]string); ok {
			if len(value) == 0 {
				nilFlag = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil, stringList",
					logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
			}
		} else {
			nilFlag = true
			logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil tran data failed, stringList",
				logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		}
	case TypeBool:
		if value, ok := dataResult.Value.(bool); ok {
			if value == false {
				nilFlag = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil, bool",
					logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
			}
		} else {
			nilFlag = true
			logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil tran data failed, bool",
				logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		}
	case TypeUint64List:
		if value, ok := dataResult.Value.([]uint64); ok {
			if len(value) == 0 {
				nilFlag = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil, uint64List",
					logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
			}
		} else {
			nilFlag = true
			logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil tran data failed, uint64List",
				logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		}
	case TypeStringListList:
		if value, ok := dataResult.Value.([][]string); ok {
			if len(value) == 0 {
				nilFlag = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil, stringListList",
					logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
			}
		} else {
			nilFlag = true
			logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil tran data failed, stringListList",
				logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		}
	}
	return nilFlag
}

func GetDataSourceParams(d map[string]traceinfo.DataSourceItem) map[string]interface{} {
	params := make(map[string]interface{})
	for k, v := range d {
		params[k] = v.Value
	}
	return params
}

func GetQuerySource(ctx context.Context, traceInfo *traceinfo.TraceInfo, field string) *traceinfo.DataSourceItem {
	if value, isOk := traceInfo.RecallConfigurationDataSource[field]; !isOk {
		logkit.FromContext(ctx).Error("RecallConfiguration GetQuerySource data invalid", logkit.String("filed", field))
		return nil
	} else {
		return &value
	}
}

type RangeItem struct {
	From        *float64 `json:"from,omitempty"`
	FromInclude bool     `json:"fromInclude,omitempty"`
	To          *float64 `json:"to,omitempty"`
	ToInclude   bool     `json:"toInclude,omitempty"`
}

func UnMarshalRange(ctx context.Context, s string) []RangeItem {
	v := make([]RangeItem, 0)
	err := json.Unmarshal([]byte(s), &v)
	if err != nil {
		logkit.FromContext(ctx).Error("RecallConfiguration DataSource UnMarshalRange failed", logkit.String("content", s))
		return nil
	}
	return v
}
