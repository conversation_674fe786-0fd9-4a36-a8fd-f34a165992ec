package parse

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic/v7"
)

func GenRecallQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallCommon *apollo.RecallCommon, recallConfig *apollo.StoreRecallConfig) *es.ESSearch {
	query := traceInfo.QueryKeyword
	if val, ok := traceInfo.RecallConfigurationDataParams["RewriteQuery_"+recallConfig.RewriteType]; ok {
		if val != nil {
			query = val.(string)
		}
	}

	// 特殊逻辑：如果涉及i2i内容，需要动态加载i2i相关数据，额外多一次I/O调用
	if abtest.GetI2iSwitch(traceInfo.AbParamClient) {
		if len(recallConfig.GetRecallCondition()) > 0 && strings.Contains(strings.ToLower(recallConfig.GetRecallCondition()), "i2i") {
			// 防止多次加载
			if _, exists := traceInfo.RecallConfigurationDataSource["FSUserI2IStoreIds"]; !exists {
				predata.InitI2IDataSource(ctx, traceInfo)
			}
		}
	}
	if len(recallConfig.GetRecallCondition()) > 0 && strings.Contains(recallConfig.GetRecallCondition(), "UserHistoricalPreferredCategories") {
		// 防止多次加载
		if _, exists := traceInfo.RecallConfigurationDataSource["UserHistoricalPreferredCategories"]; !exists {
			predata.InitUserHistoricalPreferredCategoriesDataSource(ctx, traceInfo)
		}
	}

	isRecall, err := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, recallConfig.RecallCondition)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration recall condition failed",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("condition", recallConfig.RecallCondition))
		return nil
	}
	if isRecall == false {
		return nil
	}

	// function score
	fsq, err := GenFsqQuery(ctx, traceInfo, recallConfig, recallConfig.FunctionScoreConf)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration gen fsq false",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("fsqConf", recallConfig.FunctionScoreConf))
		return nil
	}

	// script score
	scriptScore := GenScriptScoreQuery(ctx, traceInfo, recallConfig, recallConfig.ScriptScoreConf, recallCommon, recallConfig.OuterFilterConf)

	// bool query
	mustQueries, shouldQueries, mustNotQueries, filterQueries, fsqQuery := GenSearchBoolQuery(ctx, traceInfo, recallConfig, &recallConfig.SearchConf, recallConfig.OuterFilterConf, recallCommon)
	if len(mustQueries) == 0 && len(shouldQueries) == 0 && len(mustNotQueries) == 0 && len(filterQueries) == 0 && fsqQuery == nil && fsq == nil && scriptScore == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration gen search failed case must and should empty, at least faq",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("searchConf", recallConfig.SearchConf))
		return nil
	}

	sorters, err := GenSortQuery(ctx, traceInfo, recallConfig.RecallLogName, recallConfig.SortConf)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration gen sorters false",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("sortConf", recallConfig.SortConf))
		return nil
	}

	// 兼容：如果没单独配置 fsq，但是配置了 bool 的 fsq，可以采用 bool 的 fsq
	if (fsq == nil) && fsqQuery != nil {
		fsq = fsqQuery
	}

	// 超时时间
	timeout := 0
	if recallConfig.RecallTimeoutMs != nil && *recallConfig.RecallTimeoutMs > 0 {
		timeout = int(*recallConfig.RecallTimeoutMs)
	}
	q := es.ESSearch{
		IsNeedEsExplain:  traceInfo.IsNeedEsExplain,
		ESSearchType:     getEsSearchType(ctx, recallConfig),
		RecallType:       recallConfig.RecallTypeStr,
		RecallId:         recallConfig.RecallId,
		Query:            query,
		RecallTimeoutMs:  timeout,
		RecallTypeName:   recallConfig.RecallLogName,
		MustQueries:      mustQueries,
		ShouldQueries:    shouldQueries,
		MustNotQueries:   mustNotQueries,
		Sorters:          sorters,
		Filters:          filterQueries,
		FunctionQuery:    fsq,
		ScriptScoreQuery: scriptScore,
		Size:             getRecallSize(ctx, traceInfo, recallCommon, recallConfig),
	}

	if len(shouldQueries) > 0 {
		if recallConfig.SearchConf.MinimumShouldMatchStr != nil && len(*recallConfig.SearchConf.MinimumShouldMatchStr) > 0 {
			q.MinimumShouldMatchStr = *recallConfig.SearchConf.MinimumShouldMatchStr
		}
	}

	// 没有自定义 source 就用公用的 source
	if recallConfig.Source == nil {
		q.SourceInclude = recallCommon.Source
	} else {
		if len(*recallConfig.Source) == 0 {
			q.SourceInclude = nil
		} else {
			q.SourceInclude = *recallConfig.Source
		}
	}

	return &q
}

func getEsSearchType(ctx context.Context, recallConfig *apollo.StoreRecallConfig) es.EsSearchType {
	if recallConfig == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration getEsSearchType failed, recallConfig is nil")
		return es.ESSearchType_Unknown
	}

	boolQuerySearchConf := recallConfig.SearchConf
	if len(boolQuerySearchConf.MustQueries) == 0 && len(boolQuerySearchConf.MustNotQueries) == 0 &&
		len(boolQuerySearchConf.ShouldQueries) == 0 && len(boolQuerySearchConf.FilterQueries) == 0 {
		if recallConfig.ScriptScoreConf != nil {
			return es.ESSearchType_PureScriptScoreQuery
		}
		if recallConfig.FunctionScoreConf != nil {
			return es.ESSearchType_PureFunctionScoreQuery
		}
		logkit.FromContext(ctx).Error("RecallConfiguration getEsSearchType failed, no bool, no script, no fsq", logkit.Any("recallConfig", recallConfig))
	} else {
		if recallConfig.ScriptScoreConf != nil {
			return es.ESSearchType_BoolAndScriptScoreQuery
		} else if recallConfig.FunctionScoreConf != nil {
			return es.ESSearchType_BoolAndFunctionScoreQuery
		} else {
			return es.ESSearchType_PureBoolQuery
		}
	}
	return es.ESSearchType_Unknown
}

func getRecallSize(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallCommon *apollo.RecallCommon, storeRecallConfig *apollo.StoreRecallConfig) uint64 {
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("RecallConfiguration getRecallSize panic error:", logkit.Any("err", e))
			fmt.Println(e)
		}
		return
	}()

	recallSize := storeRecallConfig.RecallSize
	if storeRecallConfig.RecallSize > recallCommon.RecallMaxSize {
		recallSize = recallCommon.RecallMaxSize
	}

	// 优先使用独立配置的 RecallSizeRate
	if storeRecallConfig.RecallSizeRate != nil && *storeRecallConfig.RecallSizeRate > 0 && *storeRecallConfig.RecallSizeRate <= 1.0 {
		return uint64(float64(recallSize) * *storeRecallConfig.RecallSizeRate)
	}

	// 其次使用公共配置的 RecallSizeRate
	if recallCommon.RecallSizeRate > 0 && recallCommon.RecallSizeRate <= 1.0 {
		return uint64(float64(recallSize) * recallCommon.RecallSizeRate)
	}

	// 放弃召回比例
	logkit.FromContext(ctx).Error("RecallConfiguration getRecallSize with invalid rate, return default recallSize", logkit.String("recallName", storeRecallConfig.RecallLogName), logkit.Uint64("recallSize", recallSize))
	return recallSize
}

func GenDishRecallQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallCommon *apollo.RecallCommon, dishRecallConfig *apollo.StoreRecallConfig, storeIds []uint64) *es.ESSearch {
	isRecall, err := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, dishRecallConfig.RecallCondition)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration recall condition failed",
			logkit.String("recallName", dishRecallConfig.RecallLogName))
		return nil
	}
	if isRecall == false {
		return nil
	}

	// bool query
	mustQueries, shouldQueries, mustNotQueries, filterQueries, fsqQuery := GenSearchBoolQuery(ctx, traceInfo, dishRecallConfig, &dishRecallConfig.SearchConf, dishRecallConfig.OuterFilterConf, recallCommon)
	if len(mustQueries) == 0 && len(shouldQueries) == 0 && len(mustNotQueries) == 0 && len(filterQueries) == 0 && fsqQuery == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration gen search failed case must and should empty, at least faq",
			logkit.String("recallName", dishRecallConfig.RecallLogName), logkit.Any("searchConf", dishRecallConfig.SearchConf))
		return nil
	}
	//// addition storeId filter
	//idsFilterQuery := elastic.NewTermsQuery("store_id", convertToInterfaceSlice(storeIds)...)
	// todo: 优化idsFilterQuery
	idsFilterQuery := elastic.NewConstantScoreQuery(
		elastic.NewTermsQuery("store_id", convertToInterfaceSlice(storeIds)...),
	)

	// 将 idsFilterQuery 加入到新列表的第一个位置
	newFilterQueries := make([]elastic.Query, 0, len(filterQueries)+1)
	newFilterQueries = append([]elastic.Query{idsFilterQuery}, filterQueries...)
	filterQueries = newFilterQueries

	// 超时时间
	timeout := 500
	if dishRecallConfig.RecallTimeoutMs != nil && *dishRecallConfig.RecallTimeoutMs > 0 {
		timeout = int(*dishRecallConfig.RecallTimeoutMs)
	}

	// sorter
	sorters, err := GenSortQuery(ctx, traceInfo, dishRecallConfig.RecallLogName, dishRecallConfig.SortConf)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration gen sorters false",
			logkit.String("recallName", dishRecallConfig.RecallLogName), logkit.Any("sortConf", dishRecallConfig.SortConf))
		return nil
	}

	agg := GetTermsAggregations(ctx, traceInfo, dishRecallConfig.AggsConf)
	var recallSize uint64
	if agg != nil {
		recallSize = 0
	} else {
		recallSize = dishRecallConfig.RecallSize
	}

	q := es.ESSearch{
		IsNeedEsExplain: traceInfo.IsNeedEsExplain,
		RecallTimeoutMs: timeout,
		RecallTypeName:  dishRecallConfig.RecallName,
		RecallId:        dishRecallConfig.RecallId,

		TermsAggregation: agg,
		MustQueries:      mustQueries,
		ShouldQueries:    shouldQueries,
		MustNotQueries:   mustNotQueries,
		Filters:          filterQueries,
		FunctionQuery:    fsqQuery,
		ExactTotal:       proto.Bool(true),
		From:             0,
		Size:             recallSize,
		SourceInclude:    nil,
		Sorters:          sorters,
		Forward:          true,
		FilterPath:       nil,
	}
	return &q
}

func convertToInterfaceSlice(slice []uint64) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}
