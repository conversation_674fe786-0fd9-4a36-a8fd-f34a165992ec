package recall

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 挂菜优化: https://confluence.shopee.io/pages/viewpage.action?pageId=2520084711
func DishSearchWithDishAndCatalog(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, config apollo.DishSearcherRecallConfig) (model.DishInfos, error) {
	pt := time.Now()
	dishList := make(model.DishInfos, 0)
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishRecallDishSearcherCatalog, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromDishSearcher, len(dishList))

		if traceInfo.IsDebug {
			recallName := fmt.Sprintf("%s_%s", "HardcodeRecallDishSearcherDishWithCatalogId", recallconstant.HardcodeRecallIdDishSearcherDishWithCatalogId)
			traceInfo.AppendRecallsDishFinal(recallName)
		}
	}()
	storeIdsWithRecallDish := make([]uint64, 0)
	baseRecallDish := make([]uint64, 0)
	for _, store := range stores {
		//多路召回里，只有RewriteNer 和 EnlargeRewriteRecall 才进行挂菜
		if store.IsNeedDishRecall {
			nerOrEnlargeRecall := false
			for _, recallType := range store.RecallTypes {
				if recallType == foodalgo_search.RecallType_RewriteNer.String() || recallType == foodalgo_search.RecallType_EnlargeRewriteRecall.String() {
					nerOrEnlargeRecall = true
					break
				}
			}
			if nerOrEnlargeRecall {
				storeIdsWithRecallDish = append(storeIdsWithRecallDish, store.StoreId)
			} else {
				baseRecallDish = append(baseRecallDish, store.StoreId)
			}
		}
	}

	RewriteNerSegs := make([][]string, 0)
	for _, ner := range traceInfo.QPResult.RewriteNerResult {
		tmpFineSegs := make([]string, 0)
		if ner == nil {
			continue
		}
		for _, ner2 := range ner {
			if ner2 == nil {
				continue
			}
			tmpFineSegs = append(tmpFineSegs, ner2.GetFineGrainedSeg()...)
		}
		if len(tmpFineSegs) > 0 {
			RewriteNerSegs = append(RewriteNerSegs, tmpFineSegs)
		}
	}
	// enlarge rewrite
	EnlargeRewriteSegs := make([][]string, 0)
	for _, enlarge := range traceInfo.QPResult.EnlargeRewriteSegments {
		EnlargeRewriteSegs = append(EnlargeRewriteSegs, enlarge.Segments)
	}

	nLimit := config.RecallKeywordLimit
	if nLimit == 0 {
		nLimit = 10
	}
	nRewriteNer := len(RewriteNerSegs)
	if nRewriteNer > nLimit {
		nRewriteNer = nLimit
		RewriteNerSegs = RewriteNerSegs[0:nRewriteNer]
	}

	nEnlargeRewrite := len(EnlargeRewriteSegs)
	if nEnlargeRewrite > nLimit {
		nEnlargeRewrite = nLimit
		EnlargeRewriteSegs = EnlargeRewriteSegs[0:nEnlargeRewrite]
	}

	var baseDishIdsWithRawQuery model.DishInfos
	var dishIdsWithRawQuery model.DishInfos
	dishIdsWithRewriteNer := make([]model.DishInfos, nRewriteNer, nRewriteNer)
	dishIdsWithEnlargeRewrite := make([]model.DishInfos, nEnlargeRewrite, nEnlargeRewrite)

	filter := getDishSearcherFilter()
	sorter := getDishSearcherSorter(config.EachStoreRecallDishSize)

	group := traceInfo.ABTestGroup.GetABTestVal(apollo.SearchApolloCfg.DishRecallExpVariate)

	wg := sync.WaitGroup{}

	wg.Add(1)
	goroutine.WithGo(ctx, "BatchSBearchDish-RawQuery", func(params ...interface{}) {
		defer wg.Done()
		baseDishIdsWithRawQuery = BatchSearchDish(ctx, traceInfo, baseRecallDish, traceInfo.QPResult.Segments, traceInfo.QPResult.MaxWordSegments, group)
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "batchSearchDish-new-RawQuery", func(params ...interface{}) {
		defer wg.Done()
		keywordInfos := make([]*o2oalgo_dishsearcher.KeywordInfo, 0, len(traceInfo.QPResult.Segments)*2)
		for _, segment := range traceInfo.QPResult.Segments {
			keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
				Keyword: segment,
				Weight:  1,
			})
			if config.RawQueryUseCatalogRecall {
				keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
					Keyword: "cg_" + segment,
					Weight:  1,
				})
			}
		}

		dishIdsWithRawQuery = batchDishSearcherSearchItem(ctx, traceInfo, keywordInfos, filter, sorter, storeIdsWithRecallDish, "add")
	})

	// rewrite ner 召回菜品, 分数需要乘0.5
	for i := 0; i < nRewriteNer; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "batchSearchDish-new-ReWriteNer", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)

			keywordInfos := make([]*o2oalgo_dishsearcher.KeywordInfo, 0, len(RewriteNerSegs[index])*2)
			for _, segment := range RewriteNerSegs[index] {
				keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
					Keyword: segment,
					Weight:  1,
				})
				if config.NerRewriteUseCatalogRecall {
					keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
						Keyword: "cg_" + segment,
						Weight:  1,
					})
				}
			}
			dishIdsWithRewriteNer[index] = batchDishSearcherSearchItem(ctx, traceInfo, keywordInfos, filter, sorter, storeIdsWithRecallDish, "add")
		}, i)
	}

	// enlarge rewrite 召回菜品, 分数需要乘0.1
	for i := 0; i < nEnlargeRewrite; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "batchSearchDish-new-EnlargeRewrite", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)

			keywordInfos := make([]*o2oalgo_dishsearcher.KeywordInfo, 0, len(EnlargeRewriteSegs[index])*2)
			for _, segment := range EnlargeRewriteSegs[index] {
				keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
					Keyword: segment,
					Weight:  1,
				})
				if config.EnlargeRewriteUseCatalogRecall {
					keywordInfos = append(keywordInfos, &o2oalgo_dishsearcher.KeywordInfo{
						Keyword: "cg_" + segment,
						Weight:  1,
					})
				}
			}
			dishIdsWithEnlargeRewrite[index] = batchDishSearcherSearchItem(ctx, traceInfo, keywordInfos, filter, sorter, storeIdsWithRecallDish, "add")
		}, i)
	}

	wg.Wait()

	dishMap := make(map[uint64]*model.DishInfo, len(dishIdsWithRawQuery))
	// 原始词
	for _, info := range dishIdsWithRawQuery {
		info.Score = info.Score * config.RawQueryMultiWeight
		dishMap[info.DishId] = info
	}

	// Rewrite Ner 多词之间取最大分
	rewriteNerDishMap := make(map[uint64]*model.DishInfo)
	for i := 0; i < nRewriteNer; i++ {
		if len(dishIdsWithRewriteNer[i]) > 0 {
			for _, info := range dishIdsWithRewriteNer[i] {
				dishItem, exist := rewriteNerDishMap[info.DishId]
				if exist {
					if dishItem.Score < info.Score {
						dishItem.Score = info.Score
					}
				} else {
					rewriteNerDishMap[info.DishId] = info
				}
			}
		}
	}

	for _, info := range rewriteNerDishMap {
		dishItem, exist := dishMap[info.DishId]
		if exist {
			dishItem.Score = dishItem.Score + info.Score*config.NerRewriteMultiWeight
		} else {
			info.Score = info.Score * config.NerRewriteMultiWeight
			dishMap[info.DishId] = info
		}
	}

	// enlarge Rewrite 多词之间取最大分
	enlargeRewriteDishMap := make(map[uint64]*model.DishInfo)
	for i := 0; i < nEnlargeRewrite; i++ {
		if len(dishIdsWithEnlargeRewrite[i]) > 0 {
			for _, info := range dishIdsWithEnlargeRewrite[i] {
				dishItem, exist := enlargeRewriteDishMap[info.DishId]
				if exist {
					if dishItem.Score < info.Score {
						dishItem.Score = info.Score
					}
				} else {
					enlargeRewriteDishMap[info.DishId] = info
				}
			}
		}
	}

	for _, info := range enlargeRewriteDishMap {
		dishItem, exist := dishMap[info.DishId]
		if exist {
			dishItem.Score = dishItem.Score + info.Score*config.EnlargeRewriteMultiWeight
		} else {
			info.Score = info.Score * config.EnlargeRewriteMultiWeight
			dishMap[info.DishId] = info
		}
	}

	for _, info := range dishMap {
		dishList = append(dishList, info)
	}

	dishList = append(dishList, baseDishIdsWithRawQuery...)

	return dishList, nil
}

func batchDishSearcherSearchItem(ctx context.Context, traceInfo *traceinfo.TraceInfo, infos []*o2oalgo_dishsearcher.KeywordInfo,
	filters *o2oalgo_dishsearcher.Filter, sorters *o2oalgo_dishsearcher.Sorter,
	storeIds []uint64, calculatedMethodType string) model.DishInfos {

	if traceInfo.IsDebug {
		recallStatName := fmt.Sprintf("%s_%s", "HardcodeRecallDishSearcherDishWithCatalogId", recallconstant.HardcodeRecallIdDishSearcherDishWithCatalogId)
		traceInfo.AppendRecallsDishFinal(recallStatName)
	}

	batchSize := 150
	if apollo.SearchApolloCfg.NewDishRecallBatch != 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatch
	}
	total := len(storeIds) / batchSize
	if len(storeIds)%batchSize > 0 {
		total += 1
	}
	dishRecallList := make([]model.DishInfos, total, total)
	wg2 := sync.WaitGroup{}
	wg2.Add(total)
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.DishSearcherTimeOut
	if timeout == 0 {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()

	for i := 0; i < total; i++ {
		var tempStoreId []uint64
		if i+1 < total {
			tempStoreId = storeIds[i*batchSize : (i+1)*batchSize]
		} else {
			tempStoreId = storeIds[i*batchSize:]
		}
		goroutine.WithGo(ctx, "DishSearcherSearch", func(params ...interface{}) {
			defer wg2.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			tempIds := param[1].([]uint64)
			request := &o2oalgo_dishsearcher.SearchRequest{
				StoreId:          tempIds,
				KeywordInfo:      infos,
				RecallField:      "",
				Filter:           filters,
				Sorter:           sorters,
				AbTest:           traceInfo.ABTestGroup.GetABTestString(),
				BuyerId:          traceInfo.UserId,
				PublishId:        traceInfo.TraceRequest.PublishId,
				IsDebug:          false,
				CalculatedMethod: calculatedMethodType,
			}
			startTime := time.Now()
			resp, err := integrate.DishSearcherClient.DishSearcherSearchByRecallField(ctx, request)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "DishSearcher"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "DishSearcherByField")
			logger.MyDebug(ctx, traceInfo.IsDebug, "diff debug log: DishSearcherSearchByRecallField", zap.Any("req", request), zap.Any("response", resp))
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
				traceInfo.AddErrorToTraceInfo(err)
				logkit.FromContext(ctx).Error("dish search by field error", zap.Any("KeywordInfo", infos), zap.Error(err))
				metric_reporter.ReportClientRequestError(1, "DishSearcherByField", "failed")
				return
			}
			metric_reporter.ReportClientRequestError(1, "DishSearcherByField", "0")
			dishs := make([]*model.DishInfo, 0, len(resp.Items)*2)
			for _, item := range resp.Items {
				for _, subItem := range item.SubItems {
					dish := &model.DishInfo{
						DishId:      subItem.GetId(),
						Score:       float64(subItem.GetScore()),
						ESScore:     float64(subItem.GetScore()),
						StoreId:     item.GetId(),
						HasPicture:  subItem.GetHasPicture(),
						SalesVolume: uint32(subItem.GetSalesVolume()),
					}
					dishs = append(dishs, dish)
				}
			}
			// 直接请求菜品正排：dish searcher 返回了部分菜品正排字段，但是没有新加的saleStatus & outOfStockFlag 这两个字段，需要再调用一次正排
			dishs, _ = filling.DishMetaFilling(ctx, traceInfo, dishs)
			dishRecallList[index] = dishs
		}, i, tempStoreId)
	}
	wg2.Wait()
	nDish := 0
	if len(dishRecallList) > 0 && len(dishRecallList[0]) > 0 {
		nDish = len(dishRecallList) * len(dishRecallList[0])
	}
	dishList := make([]*model.DishInfo, 0, nDish)
	for i := range dishRecallList {
		if dishRecallList[i] != nil {
			dishList = append(dishList, dishRecallList[i]...)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish recall by dish and catalog result", zap.Any("keyword", infos), zap.Any("storeIDs", storeIds), zap.Any("dish", dishList))
	return dishList
}

func getDishSearcherFilter() *o2oalgo_dishsearcher.Filter {
	filterList := make([]*o2oalgo_dishsearcher.EachFilter, 0, 3)
	filterList = append(filterList, &o2oalgo_dishsearcher.EachFilter{
		Type:      1,
		Name:      "available",
		IntValues: []int64{0},
	})
	filterList = append(filterList, &o2oalgo_dishsearcher.EachFilter{
		Type:      1,
		Name:      "listing_status",
		IntValues: []int64{0},
	})

	filter := &o2oalgo_dishsearcher.Filter{ItemFilter: filterList}
	return filter
}

func getDishSearcherSorter(limit int) *o2oalgo_dishsearcher.Sorter {
	sortList := make([]*o2oalgo_dishsearcher.EachSorter, 0, 4)
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "score",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "has_picture",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "sales_volume",
		Order: "desc",
	})
	sortList = append(sortList, &o2oalgo_dishsearcher.EachSorter{
		Name:  "id",
		Order: "asc",
	})
	if limit == 0 {
		limit = 2
	}

	sort := &o2oalgo_dishsearcher.Sorter{
		ItemSort:   sortList,
		ItemMaxNum: int32(limit),
	}
	return sort
}
