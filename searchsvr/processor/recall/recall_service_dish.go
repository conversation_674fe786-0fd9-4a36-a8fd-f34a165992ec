package recall

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

type DishRecall struct {
	DishQueries       []*es.ESSearch
	DishRecallConfigs []*apollo.StoreRecallConfig
	recallCommon      *apollo.RecallCommon
}

func (r *DishRecall) initEveryConfig(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, common *apollo.RecallCommon) {
	// 初始化生成日志打印时的 recallName
	recallConfig.RecallLogName = recallConfig.RecallName + "_" + recallConfig.RecallId

	// 初始化超时时间
	if recallConfig.RecallTimeoutMs == nil && common.RecallTimeoutMs > 0 {
		recallConfig.RecallTimeoutMs = proto.Uint64(common.RecallTimeoutMs)
	}

	// 初始化是否降级
	if recallConfig.IsDownGrade == nil {
		recallConfig.IsDownGrade = proto.Bool(common.IsDownGrade)
	}

	// 召回比例
	if recallConfig.RecallSizeRate == nil && common.RecallSizeRate > 0 && common.RecallSizeRate <= 1.0 {
		recallConfig.RecallSizeRate = proto.Float64(common.RecallSizeRate)
	}
}

func (r *DishRecall) BuildDishRecall(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) *DishRecall {
	recallConfig := parse.GetDishRecallConfig(ctx, traceInfo)
	if recallConfig == nil || len(recallConfig.DishRecalls) == 0 {
		logkit.FromContext(ctx).Error("RecallConfiguration RecallService BuildDishRecall failed, get no recall config")
		return nil
	}

	recallConfigs := recallConfig.DishRecalls
	r.DishQueries = make([]*es.ESSearch, 0)
	r.DishRecallConfigs = make([]*apollo.StoreRecallConfig, 0)
	r.recallCommon = recallConfig.DishCommon

	for i := range recallConfigs {
		r.initEveryConfig(ctx, traceInfo, recallConfigs[i], r.recallCommon)
		q := parse.GenDishRecallQuery(ctx, traceInfo, r.recallCommon, recallConfigs[i], storeIds)
		if q == nil {
			continue
		}
		if recallConfigs[i].IsDownGrade != nil && *recallConfigs[i].IsDownGrade == true {
			continue
		}
		if !traceInfo.IsDebug {
			q.FilterPath = []string{"aggregations", "took"}
			q.SourceInclude = []string{}
		}

		q.RecallConfig = recallConfigs[i]
		r.DishRecallConfigs = append(r.DishRecallConfigs, recallConfigs[i])
		r.DishQueries = append(r.DishQueries, q)
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("RecallConfiguration BuildDishRecall dsl", logkit.String("recallName", recallConfigs[i].RecallName), logkit.String("dsl", q.DslString(ctx)))
		}
	}

	if traceInfo.IsDebug {
		if len(r.DishRecallConfigs) > 0 {
			firstConfig := r.DishRecallConfigs[0]
			traceInfo.AppendRecallsDishFinal(fmt.Sprintf("%s_%s", firstConfig.RecallName, firstConfig.RecallId))

			for _, x := range r.DishRecallConfigs[1:] {
				traceInfo.AppendRecallsDishConditionOkButGiveUp(fmt.Sprintf("%s_%s", x.RecallName, x.RecallId))
			}
		}
	}

	return r
}

func (r *DishRecall) BuildDishRecallV2(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) *DishRecall {
	recallConfig := parse.GetDishRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainDish, recallconstant.RecallDataSourceES)
	if recallConfig == nil {
		logkit.FromContext(ctx).Error("BuildDishRecallV2  recall config is nil")
		return nil
	}

	recallConfigs := recallConfig.DishRecalls
	r.DishQueries = make([]*es.ESSearch, 0)
	r.DishRecallConfigs = make([]*apollo.StoreRecallConfig, 0)
	r.recallCommon = recallConfig.DishCommon

	for i := range recallConfigs {
		// 新接口只召回listing的菜品召回
		if recallConfigs[i].RecallCondition != nil && !strings.Contains(*recallConfigs[i].RecallCondition, "IsDishListing") {
			continue
		}

		r.initEveryConfig(ctx, traceInfo, recallConfigs[i], r.recallCommon)
		q := parse.GenDishRecallQuery(ctx, traceInfo, r.recallCommon, recallConfigs[i], storeIds)
		if q == nil {
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("RecallConfiguration BuildDishRecall recallConfig is nil", logkit.String("recallName", recallConfigs[i].RecallName))
			}
			continue
		}
		if recallConfigs[i].IsDownGrade != nil && *recallConfigs[i].IsDownGrade == true {
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("RecallConfiguration BuildDishRecall recallConfig is downGrade", logkit.String("recallName", recallConfigs[i].RecallName))
			}
			continue
		}
		if q.TermsAggregation != nil {
			q.FilterPath = []string{
				"aggregations.dish_aggregation.buckets.key",
				"aggregations.dish_aggregation.buckets.top_dishes.hits.hits._id",
				"aggregations.dish_aggregation.buckets.top_dishes.hits.hits.sort",
				"took",
			}
		} else {
			q.FilterPath = []string{"hits.hits._id", "took"}
		}

		q.SourceInclude = []string{}

		q.RecallConfig = recallConfigs[i]
		r.DishRecallConfigs = append(r.DishRecallConfigs, recallConfigs[i])
		r.DishQueries = append(r.DishQueries, q)
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("RecallConfiguration BuildDishRecall dsl", logkit.String("recallName", recallConfigs[i].RecallName), logkit.String("dsl", q.DslString(ctx)))
		}
	}

	if traceInfo.IsDebug {
		if len(r.DishRecallConfigs) > 0 {
			for _, x := range r.DishRecallConfigs {
				traceInfo.AppendRecallsDishFinal(fmt.Sprintf("%s_%s", x.RecallName, x.RecallId))
			}
		}
	}

	return r
}
